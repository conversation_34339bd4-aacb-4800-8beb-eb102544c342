# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv

    主要功能：
    1. 摄像头固定中央的功能和观察空间扁平化
    2. 课程学习 (Curriculum Learning)：
       - 关卡1 (0-1000回合)：3米直线停车，学会基础控制
       - 关卡2 (1000-3000回合)：5-8米距离+轻微偏移，学会修正
       - 关卡3 (3000+回合)：完全随机场景，真实停车
    3. 引导奖励 (Guidance Reward)：
       - 基于车头朝向与目标方向的夹角
       - 使用余弦函数平滑映射：cos(angle_diff)
       - 与基础奖励组合，权重0.2

    设计理念：通过课程学习让智能体自然学会停车技能
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

        # 新增：为课程学习添加一个全局共享的回合计数器
        # 使用类变量确保所有并行环境共享同一个计数器
        if not hasattr(CustomParkingEnv, '_global_episode_count'):
            CustomParkingEnv._global_episode_count = 0

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """
        重写 reset 方法以实现课程学习 (Curriculum Learning)
        """
        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        # 这一步是必须的，它会设置好目标（goal）等场景信息
        obs, info = super().reset(seed=seed, options=options)

        # 更新全局回合计数器（所有并行环境共享）
        CustomParkingEnv._global_episode_count += 1
        current_episode = CustomParkingEnv._global_episode_count

        # --- 课程学习的核心逻辑 ---

        # 关卡1: 新手村 (前1000个回合)
        # 任务：学会直行和刹车
        if current_episode < 1000:
            goal_pos = self.vehicle.goal.position
            goal_heading = self.vehicle.goal.heading
            # 将车辆精确地放在目标正前方3米处，车头完美对齐
            start_pos = goal_pos - 3 * np.array([np.cos(goal_heading), np.sin(goal_heading)])
            self.vehicle.position = start_pos
            self.vehicle.heading = goal_heading

        # 关卡2: 初级训练 (1000到3000回合)
        # 任务：学会在稍远距离、有轻微随机性的情况下开直线
        elif current_episode < 3000:
            goal_pos = self.vehicle.goal.position
            goal_heading = self.vehicle.goal.heading
            # 放在目标正前方一个5到8米的随机距离
            random_distance = np.random.uniform(5, 8)
            # 增加一点点横向的随机偏移
            random_offset = np.random.uniform(-0.5, 0.5)
            offset_vector = random_offset * np.array([-np.sin(goal_heading), np.cos(goal_heading)])

            start_pos = goal_pos - random_distance * np.array([np.cos(goal_heading), np.sin(goal_heading)]) + offset_vector
            self.vehicle.position = start_pos
            self.vehicle.heading = goal_heading  # 朝向依然对齐

        # 关卡3: 专家模式 (3000回合后)
        # 不做任何修改，直接使用父类生成的完全随机场景
        else:
            pass

        # 可选：打印课程学习进度（每100回合打印一次）
        if current_episode % 100 == 0:
            if current_episode < 1000:
                stage = "关卡1-新手村"
            elif current_episode < 3000:
                stage = "关卡2-初级训练"
            else:
                stage = "关卡3-专家模式"
            print(f"课程学习进度: 第{current_episode}回合 - {stage}")

        # 因为我们可能手动修改了车辆状态，需要刷新观察值
        # 技巧：执行一个零动作来获取当前状态的正确观察值
        # 这确保了返回给智能体的 'obs' 是更新后位置的观察
        self.vehicle.velocity = np.zeros_like(self.vehicle.velocity)
        self.vehicle.action = {'acceleration': 0, 'steering': 0}
        obs = self.observation_type.observe()
        obs = self._flatten_observation(obs)

        return obs, info

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)
        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def compute_reward(self, achieved_goal: np.ndarray, desired_goal: np.ndarray, info: dict, p: float = 0.5) -> float:
        """计算奖励值 - 调用父类方法"""
        return super().compute_reward(achieved_goal, desired_goal, info, p)

    def _reward(self, action: np.ndarray) -> float:
        """
        使用引导奖励来增强父类的奖励函数
        """
        # 1. 获取父类的基础奖励（包含距离奖励和碰撞惩罚）
        # 这是我们的基础分
        base_reward = super()._reward(action)

        # 2. 计算引导奖励 (我们的"方向罗盘")
        guidance_reward = self._get_guidance_reward()

        # 3. 将两者组合
        # 我们将引导奖励作为一个加分项。权重0.2是一个经验值，你可以根据效果调整。
        # 如果智能体还是乱开，可以适当增大这个权重。
        total_reward = base_reward + 0.2 * guidance_reward

        return total_reward

    def _get_guidance_reward(self) -> float:
        """
        计算引导奖励 (Guidance Reward)，借鉴自论文。
        这个奖励的核心是：车头朝向与目标方向的夹角越小，奖励越高。
        奖励值范围: [-1, 1]。
        """
        # 提取所需信息
        vehicle_pos = self.vehicle.position
        vehicle_heading = self.vehicle.heading
        goal_pos = self.vehicle.goal.position

        # 计算从车辆指向目标的方向向量
        direction_to_goal = goal_pos - vehicle_pos

        # 计算这个方向向量的角度（atan2能正确处理所有象限）
        angle_to_goal = np.arctan2(direction_to_goal[1], direction_to_goal[0])

        # 计算车辆当前朝向与目标方向之间的角度差
        # 技巧：使用 (a - b + pi) % (2*pi) - pi 可以快速得到在[-π, π]范围内的角度差
        angle_diff = (vehicle_heading - angle_to_goal + np.pi) % (2 * np.pi) - np.pi

        # 使用余弦函数将角度差平滑地映射到奖励值
        # 如果方向完全正确 (angle_diff=0), cos(0) = 1, 得到最高奖励
        # 如果方向完全错误 (angle_diff=π), cos(π) = -1, 得到最大惩罚
        guidance_reward = np.cos(angle_diff)

        return guidance_reward

    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """简化成功判定：直接使用父类逻辑"""
        return super()._is_success(achieved_goal, desired_goal)

    def _is_terminated(self) -> bool:
        """判断回合是否结束 - 调用父类方法"""
        return super()._is_terminated()

    def _is_truncated(self) -> bool:
        """判断回合是否因为超时而终止 - 调用父类方法"""
        return super()._is_truncated()

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self):
        """
        重写渲染方法，设置固定摄像头视角
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result



def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    推荐配置示例：
        config = {
            'collision_reward': -5,           # 适中的碰撞惩罚
            'max_episode_steps': 100,         # 足够的探索时间
            'env_seed': 42,                   # 随机种子
        }

    课程学习训练效果：
        - 关卡1（0-1000 episodes）：3米直线停车，快速学会基础控制
        - 关卡2（1000-3000 episodes）：5-8米+偏移，学会修正和调整
        - 关卡3（3000+ episodes）：随机场景，真实停车技能

    引导奖励特点：
        - 基于朝向的方向引导：cos(angle_diff)
        - 权重0.2，与基础奖励组合
        - 帮助智能体快速学会"朝向目标"的概念

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
