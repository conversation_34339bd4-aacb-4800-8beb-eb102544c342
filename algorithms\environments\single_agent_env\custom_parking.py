# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv

    主要改进：
    1. 摄像头固定中央的功能和观察空间扁平化
    2. 融合式奖励函数设计：
       - 阶段性速度引导：远快近慢，自然形成最优驾驶模式
       - 朝向奖励：远距离指向目标，近距离对齐朝向
       - 平滑驾驶：防止危险的高速急转，保持探索自由
       - 动态权重：根据距离自动调整各奖励组件的重要性
    3. 渐进式成功标准：根据训练进度自动调整难度
    4. 可选的物理约束增强：让高速转向自然变困难

    设计理念：不过度规则化，让智能体自然学会停车技能
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境，清理历史状态并更新训练计数器"""
        # 清理所有历史状态
        for attr in ['last_distance', 'steering_history', 'speed_penalty']:
            if hasattr(self, attr):
                delattr(self, attr)

        # 更新训练计数器（用于渐进式成功标准）
        if not hasattr(self, 'training_episodes'):
            self.training_episodes = 0
        self.training_episodes += 1

        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)
        obs = self._flatten_observation(obs)
        return obs, info

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        # 可选：添加物理真实性约束
        if hasattr(self.custom_config, 'enable_physics_constraints') and self.custom_config.enable_physics_constraints:
            action = self._apply_physics_constraints(action)

        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)
        return obs, reward, terminated, truncated, info

    def _apply_physics_constraints(self, action: np.ndarray) -> np.ndarray:
        """可选的物理约束：模拟高速转向的自然困难"""
        speed = np.linalg.norm(self.vehicle.velocity)
        steering = abs(action[1]) if len(action) > 1 else 0

        # 高速转向时临时降低车辆的加速能力（模拟离心力影响）
        if speed > 5 and steering > 0.5:
            # 不直接修改action，而是影响车辆的物理参数
            # 这会在下一步自然体现在车辆行为中
            if hasattr(self.vehicle, 'MAX_ACCELERATION'):
                self.vehicle.MAX_ACCELERATION = 3.0  # 临时降低加速能力
        else:
            if hasattr(self.vehicle, 'MAX_ACCELERATION'):
                self.vehicle.MAX_ACCELERATION = 5.0  # 恢复正常加速能力

        return action

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def compute_reward(self, achieved_goal: np.ndarray, desired_goal: np.ndarray, info: dict, p: float = 0.5) -> float:
        """计算奖励值 - 调用父类方法"""
        return super().compute_reward(achieved_goal, desired_goal, info, p)

    def _reward(self, action: np.ndarray) -> float:
        """融合方案：简洁有效的奖励函数，让智能体自然学会停车"""
        # 获取基础信息
        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position
        distance = np.linalg.norm(vehicle_pos - target_pos)
        speed = np.linalg.norm(self.vehicle.velocity)

        # 1. 基础奖励（来自父类：距离奖励 + 碰撞惩罚）
        base_reward = super()._reward(action)

        # 2. 阶段性速度引导（远快近慢，自然形成）
        speed_reward = self._get_adaptive_speed_guidance(distance, speed)

        # 3. 朝向引导（停车必需，越近越重要）
        orientation_reward = self._get_orientation_bonus(distance)

        # 4. 平滑驾驶奖励（防止危险驾驶）
        smoothness_reward = self._get_smoothness_bonus(action, speed)

        # 5. 动态权重组合（根据距离自动调整重点）
        if distance > 10:
            # 远距离：快速接近最重要
            total_reward = (base_reward +
                          0.3 * speed_reward +
                          0.1 * orientation_reward +
                          0.1 * smoothness_reward -
                          0.01)  # 时间压力
        elif distance > 5:
            # 中距离：开始考虑朝向
            total_reward = (base_reward +
                          0.2 * speed_reward +
                          0.3 * orientation_reward +
                          0.1 * smoothness_reward -
                          0.01)
        else:
            # 近距离：精确控制最重要
            total_reward = (base_reward +
                          0.1 * speed_reward +
                          0.5 * orientation_reward +
                          0.2 * smoothness_reward -
                          0.01)

        # 调试信息（可选开启）
        if hasattr(self.custom_config, 'debug_rewards') and self.custom_config.debug_rewards:
            if hasattr(self, 'step_count'):
                self.step_count += 1
            else:
                self.step_count = 1

            if self.step_count % 50 == 0:  # 每50步打印一次
                print(f"距离: {distance:.2f}, 速度: {speed:.2f}")
                print(f"基础奖励: {base_reward:.3f}, 速度奖励: {speed_reward:.3f}")
                print(f"朝向奖励: {orientation_reward:.3f}, 平滑奖励: {smoothness_reward:.3f}")
                print(f"总奖励: {total_reward:.3f}")
                print("---")

        return total_reward

    def _get_adaptive_speed_guidance(self, distance: float, speed: float) -> float:
        """阶段性速度引导：远快近慢，平滑过渡"""
        # 根据距离确定理想速度和容忍范围
        if distance > 15:
            ideal_speed = 15.0
            tolerance = 5.0
        elif distance > 5:
            ideal_speed = 8.0
            tolerance = 3.0
        else:
            ideal_speed = 2.0
            tolerance = 1.0

        # 计算速度偏差
        speed_diff = abs(speed - ideal_speed)

        # 平滑的奖励计算（避免复杂的高斯函数）
        if speed_diff < tolerance:
            return 0.2 * (1 - speed_diff / tolerance)  # 线性衰减奖励
        else:
            return -0.1  # 轻微惩罚偏离理想速度

    def _get_orientation_bonus(self, distance: float) -> float:
        """朝向奖励：远距离指向目标，近距离对齐朝向"""
        if not hasattr(self.vehicle, 'goal') or not hasattr(self.vehicle.goal, 'heading'):
            return 0.0

        vehicle_heading = self.vehicle.heading

        # 根据距离选择目标朝向
        if distance > 5:
            # 远距离：朝向应该指向目标位置
            direction_to_goal = np.arctan2(
                self.vehicle.goal.position[1] - self.vehicle.position[1],
                self.vehicle.goal.position[0] - self.vehicle.position[0]
            )
            target_heading = direction_to_goal
        else:
            # 近距离：朝向应该与目标朝向一致
            target_heading = self.vehicle.goal.heading

        # 计算角度差（取最小角度）
        heading_diff = abs(vehicle_heading - target_heading)
        heading_diff = min(heading_diff, 2 * np.pi - heading_diff)

        # 转换为奖励（使用余弦函数，范围-1到0）
        base_reward = (np.cos(heading_diff) - 1) * 0.5

        # 距离越近，朝向越重要
        if distance < 5:
            base_reward *= 2  # 近距离时朝向加倍重要

        return base_reward

    def _get_smoothness_bonus(self, action: np.ndarray, speed: float) -> float:
        """平滑驾驶奖励：防止危险驾驶，保持探索自由"""
        steering = abs(action[1]) if len(action) > 1 else 0

        # 明确惩罚危险的高速急转
        if speed > 10 and steering > 0.5:
            return -0.5  # 这种组合在物理上就很困难

        # 一般情况下轻微鼓励平滑驾驶
        return -steering * 0.1  # 轻微惩罚大幅转向，但不过度限制

    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """修复的成功判定：先使用父类逻辑，再添加渐进式标准"""
        # 首先使用父类的成功判定作为基础
        parent_success = super()._is_success(achieved_goal, desired_goal)

        # 如果父类判定失败，直接返回失败
        if not parent_success:
            return False

        # 如果父类判定成功，再检查我们的额外标准
        position_error = np.linalg.norm(achieved_goal - desired_goal)
        speed = np.linalg.norm(self.vehicle.velocity)

        # 朝向检查（如果可用）
        heading_error = 0
        if hasattr(self.vehicle, 'heading') and hasattr(self.vehicle.goal, 'heading'):
            heading_error = abs(self.vehicle.heading - self.vehicle.goal.heading)
            heading_error = min(heading_error, 2 * np.pi - heading_error)

        # 调试信息
        if hasattr(self.custom_config, 'debug_success') and self.custom_config.debug_success:
            print(f"成功判定 - 位置误差: {position_error:.3f}, 速度: {speed:.3f}, 朝向误差: {np.degrees(heading_error):.1f}°")

        # 在测试阶段使用严格标准，训练阶段使用渐进标准
        if hasattr(self, 'training_episodes') and self.training_episodes > 0:
            # 训练阶段：渐进式标准
            if self.training_episodes < 1000:
                final_success = position_error < 2.0 and speed < 5.0
            elif self.training_episodes < 5000:
                final_success = position_error < 1.5 and speed < 3.0 and heading_error < np.radians(30)
            else:
                final_success = position_error < 1.0 and speed < 2.0 and heading_error < np.radians(15)
        else:
            # 测试阶段：使用严格标准
            final_success = position_error < 1.0 and speed < 2.0 and heading_error < np.radians(20)

        if hasattr(self.custom_config, 'debug_success') and self.custom_config.debug_success:
            print(f"父类判定: {parent_success}, 最终判定: {final_success}")

        return final_success

    def debug_current_state(self):
        """调试当前状态 - 临时方法"""
        if hasattr(self.vehicle, 'position') and hasattr(self.vehicle, 'goal'):
            vehicle_pos = self.vehicle.position
            target_pos = self.vehicle.goal.position
            distance = np.linalg.norm(vehicle_pos - target_pos)
            speed = np.linalg.norm(self.vehicle.velocity)

            print(f"车辆位置: {vehicle_pos}")
            print(f"目标位置: {target_pos}")
            print(f"距离: {distance:.3f}")
            print(f"速度: {speed:.3f}")

            if hasattr(self.vehicle, 'heading') and hasattr(self.vehicle.goal, 'heading'):
                print(f"车辆朝向: {np.degrees(self.vehicle.heading):.1f}°")
                print(f"目标朝向: {np.degrees(self.vehicle.goal.heading):.1f}°")

            # 测试父类成功判定
            obs = self.observation_type_parking.observe()
            if isinstance(obs, dict):
                parent_success = super()._is_success(obs["achieved_goal"], obs["desired_goal"])
                print(f"父类成功判定: {parent_success}")

    def _is_terminated(self) -> bool:
        """判断回合是否结束 - 调用父类方法"""
        return super()._is_terminated()

    def _is_truncated(self) -> bool:
        """判断回合是否因为超时而终止 - 调用父类方法"""
        return super()._is_truncated()

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self):
        """
        重写渲染方法，设置固定摄像头视角
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result



def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    推荐配置示例：
        config = {
            'collision_reward': -5,           # 适中的碰撞惩罚
            'max_episode_steps': 100,         # 足够的探索时间
            'enable_physics_constraints': False,  # 可选的物理约束增强
            'env_seed': 42,                   # 随机种子
        }

    预期训练效果：
        - 初期（0-1000 episodes）：学会基本接近目标
        - 中期（1000-5000 episodes）：形成远快近慢模式，开始注意朝向
        - 后期（5000+ episodes）：平滑停车，成功率80%+

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
