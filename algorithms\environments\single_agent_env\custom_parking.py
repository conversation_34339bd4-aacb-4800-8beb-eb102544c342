# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv

    主要功能：
    1. 摄像头固定中央的功能和观察空间扁平化
    2. 自适应课程学习 (Adaptive Curriculum Learning)：
       - 关卡1：3米直线停车，学会基础控制
       - 关卡2：5-8米距离+轻微偏移，学会修正
       - 关卡3：完全随机场景，真实停车
       - 自动晋级：成功率达到80%时自动进入下一关卡
    3. 引导奖励 (Guidance Reward)：
       - 基于车头朝向与目标方向的夹角
       - 使用余弦函数平滑映射：cos(angle_diff)
       - 与基础奖励组合，权重0.2
       - 碰撞时屏蔽引导奖励，避免"正确姿势撞墙"的误导

    设计理念：通过课程学习让智能体自然学会停车技能，确保安全第一
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

        # 新增：自适应课程学习系统
        # 使用全局共享的难度等级，所有并行环境同步
        if not hasattr(CustomParkingEnv, '_global_difficulty_level'):
            CustomParkingEnv._global_difficulty_level = 1  # 初始难度为1

        # 检查是否需要重置课程学习（用于新的训练会话）
        if hasattr(config, 'reset_curriculum') and config.reset_curriculum:
            CustomParkingEnv._global_difficulty_level = 1
            print("课程学习难度已重置为关卡1")

        # 保留回合计数器用于调试和监控
        if not hasattr(CustomParkingEnv, '_global_episode_count'):
            CustomParkingEnv._global_episode_count = 0

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """
        重写 reset 方法以实现课程学习 (Curriculum Learning)
        """
        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        # 这一步是必须的，它会设置好目标（goal）等场景信息
        obs, info = super().reset(seed=seed, options=options)

        # 更新全局回合计数器（用于监控和调试）
        CustomParkingEnv._global_episode_count += 1
        current_episode = CustomParkingEnv._global_episode_count

        # --- 自适应课程学习的核心逻辑 ---
        # 根据当前难度等级设置场景，而不是固定的回合数

        current_level = CustomParkingEnv._global_difficulty_level

        if current_level == 1:
            # 关卡1: 新手村 - 学会直行和刹车
            goal_pos = self.vehicle.goal.position
            goal_heading = self.vehicle.goal.heading
            # 将车辆精确地放在目标正前方3米处，车头完美对齐
            start_pos = goal_pos - 3 * np.array([np.cos(goal_heading), np.sin(goal_heading)])
            self.vehicle.position = start_pos
            self.vehicle.heading = goal_heading

        elif current_level == 2:
            # 关卡2: 初级训练 - 学会在稍远距离、有轻微随机性的情况下开直线
            goal_pos = self.vehicle.goal.position
            goal_heading = self.vehicle.goal.heading
            # 放在目标正前方一个5到8米的随机距离
            random_distance = np.random.uniform(5, 8)
            # 增加一点点横向的随机偏移
            random_offset = np.random.uniform(-0.5, 0.5)
            offset_vector = random_offset * np.array([-np.sin(goal_heading), np.cos(goal_heading)])

            start_pos = goal_pos - random_distance * np.array([np.cos(goal_heading), np.sin(goal_heading)]) + offset_vector
            self.vehicle.position = start_pos
            self.vehicle.heading = goal_heading  # 朝向依然对齐

        else:  # current_level == 3
            # 关卡3: 专家模式 - 完全随机场景
            # 不做任何修改，直接使用父类生成的完全随机场景
            pass

        # 可选：打印课程学习进度（每100回合打印一次）
        if current_episode % 100 == 0:
            level_names = {1: "关卡1-新手村", 2: "关卡2-初级训练", 3: "关卡3-专家模式"}
            stage = level_names.get(current_level, "未知关卡")
            print(f"自适应课程学习: 第{current_episode}回合 - {stage}")

        # 因为我们手动修改了车辆状态，需要刷新观察值
        # 确保车辆速度为0（刚重置的状态）
        self.vehicle.velocity = np.zeros_like(self.vehicle.velocity)
        self.vehicle.action = {'acceleration': 0, 'steering': 0}

        # 直接获取新观察值，避免多余的物理步进
        obs = self.observation_type.observe()
        obs = self._flatten_observation(obs)

        return obs, info

    def set_curriculum_level(self, success_rate: float):
        """
        根据外部传入的成功率来更新难度等级（自适应课程学习）

        Args:
            success_rate: 最近N个回合的平均成功率 (0.0 - 1.0)
        """
        SUCCESS_THRESHOLD = 0.8  # 成功率达到80%就晋级
        current_level = CustomParkingEnv._global_difficulty_level

        if current_level == 1 and success_rate >= SUCCESS_THRESHOLD:
            CustomParkingEnv._global_difficulty_level = 2
            print(f"🎉 表现出色！成功率达到{success_rate:.2f}，晋级到【关卡2-初级训练】！")

        elif current_level == 2 and success_rate >= SUCCESS_THRESHOLD:
            CustomParkingEnv._global_difficulty_level = 3
            print(f"🎉 表现出色！成功率达到{success_rate:.2f}，晋级到【关卡3-专家模式】！")

        elif current_level == 3:
            # 已经是最高难度，保持不变
            pass

    def get_current_curriculum_info(self) -> dict:
        """获取当前课程学习信息"""
        level_names = {1: "关卡1-新手村", 2: "关卡2-初级训练", 3: "关卡3-专家模式"}
        return {
            'difficulty_level': CustomParkingEnv._global_difficulty_level,
            'level_name': level_names.get(CustomParkingEnv._global_difficulty_level, "未知"),
            'total_episodes': CustomParkingEnv._global_episode_count
        }

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)
        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def compute_reward(self, achieved_goal: np.ndarray, desired_goal: np.ndarray, info: dict, p: float = 0.5) -> float:
        """计算奖励值 - 调用父类方法"""
        return super().compute_reward(achieved_goal, desired_goal, info, p)

    def _reward(self, action: np.ndarray) -> float:
        """
        改进后的奖励函数，解决了奖励冲突问题
        """
        # 如果车辆已经撞了，立即返回一个大的负奖励，并且不进行后续计算
        # 这是为了确保碰撞是智能体最不希望发生的事情，避免"以正确姿势撞墙"的误导
        if self.vehicle.crashed:
            return -10.0  # 强烈的碰撞惩罚，比默认的-5更严厉

        # 只有在安全的情况下，才计算和组合奖励
        # 1. 获取父类的基础奖励（距离奖励）
        base_reward = super()._reward(action)

        # 2. 计算引导奖励 (我们的"方向罗盘")
        guidance_reward = self._get_guidance_reward()

        # 3. 将两者组合
        # 权重0.2是一个经验值，你可以根据效果调整
        total_reward = base_reward + 0.2 * guidance_reward

        return total_reward

    def _get_guidance_reward(self) -> float:
        """
        计算引导奖励 (Guidance Reward)，借鉴自论文。
        这个奖励的核心是：车头朝向与目标方向的夹角越小，奖励越高。
        奖励值范围: [-1, 1]。
        """
        # 提取所需信息
        vehicle_pos = self.vehicle.position
        vehicle_heading = self.vehicle.heading
        goal_pos = self.vehicle.goal.position

        # 计算从车辆指向目标的方向向量
        direction_to_goal = goal_pos - vehicle_pos

        # 计算这个方向向量的角度（atan2能正确处理所有象限）
        angle_to_goal = np.arctan2(direction_to_goal[1], direction_to_goal[0])

        # 计算车辆当前朝向与目标方向之间的角度差
        # 技巧：使用 (a - b + pi) % (2*pi) - pi 可以快速得到在[-π, π]范围内的角度差
        angle_diff = (vehicle_heading - angle_to_goal + np.pi) % (2 * np.pi) - np.pi

        # 使用余弦函数将角度差平滑地映射到奖励值
        # 如果方向完全正确 (angle_diff=0), cos(0) = 1, 得到最高奖励
        # 如果方向完全错误 (angle_diff=π), cos(π) = -1, 得到最大惩罚
        guidance_reward = np.cos(angle_diff)

        return guidance_reward

    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """简化成功判定：直接使用父类逻辑"""
        return super()._is_success(achieved_goal, desired_goal)

    def _is_terminated(self) -> bool:
        """判断回合是否结束 - 调用父类方法"""
        return super()._is_terminated()

    def _is_truncated(self) -> bool:
        """判断回合是否因为超时而终止 - 调用父类方法"""
        return super()._is_truncated()

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self):
        """
        重写渲染方法，设置固定摄像头视角
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result



class AdaptiveCurriculumTracker:
    """
    自适应课程学习追踪器

    用于在训练循环中监控智能体表现，并自动调整环境难度
    """

    def __init__(self, window_size: int = 100, success_threshold: float = 0.8):
        """
        初始化追踪器

        Args:
            window_size: 用于计算成功率的窗口大小（最近N个回合）
            success_threshold: 晋级所需的成功率阈值
        """
        self.window_size = window_size
        self.success_threshold = success_threshold
        self.performance_window = []
        self.total_episodes = 0

    def update(self, is_success: bool) -> float:
        """
        更新表现记录并返回当前成功率

        Args:
            is_success: 当前回合是否成功

        Returns:
            float: 当前窗口内的成功率 (0.0 - 1.0)
        """
        self.total_episodes += 1
        self.performance_window.append(is_success)

        # 保持窗口大小
        if len(self.performance_window) > self.window_size:
            self.performance_window.pop(0)

        # 计算成功率
        if len(self.performance_window) >= self.window_size:
            success_rate = sum(self.performance_window) / len(self.performance_window)
        else:
            success_rate = 0.0  # 窗口还没满，不计算

        return success_rate

    def get_stats(self) -> dict:
        """获取当前统计信息"""
        if len(self.performance_window) > 0:
            current_success_rate = sum(self.performance_window) / len(self.performance_window)
        else:
            current_success_rate = 0.0

        return {
            'total_episodes': self.total_episodes,
            'window_size': len(self.performance_window),
            'success_rate': current_success_rate,
            'ready_for_evaluation': len(self.performance_window) >= self.window_size
        }


def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    推荐配置示例：
        config = {
            'collision_reward': -5,           # 适中的碰撞惩罚
            'max_episode_steps': 100,         # 足够的探索时间
            'env_seed': 42,                   # 随机种子
            'reset_curriculum': True,         # 重置课程学习进度
        }

    自适应课程学习特点：
        - 关卡1：3米直线停车，学会基础控制
        - 关卡2：5-8米+偏移，学会修正和调整
        - 关卡3：随机场景，真实停车技能
        - 自动晋级：成功率达到80%时自动进入下一关卡

    引导奖励特点：
        - 基于朝向的方向引导：cos(angle_diff)
        - 权重0.2，与基础奖励组合
        - 碰撞时屏蔽引导奖励，确保安全第一

    使用示例：
        # 在训练循环中
        tracker = AdaptiveCurriculumTracker()
        for episode in range(total_episodes):
            # ... 运行回合 ...
            success_rate = tracker.update(is_success)
            if tracker.get_stats()['ready_for_evaluation']:
                env.set_curriculum_level(success_rate)

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
